// ==UserScript==
// @name         Japanese Ruby Text Converter
// @namespace    http://tampermonkey.net/
// @version      3.6
// @description  Convert Japanese text with readings from parentheses format to ruby tags with compound word segmentation
// <AUTHOR>
// @match        https://www.edewakaru.com/*
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @run-at       document-start
// ==/UserScript==

;(function () {
  ;('use strict')

  // --- 配置区域 ---
  // 复合词列表（支持多种格式）
  const COMPOUND_WORDS = [
    // 标准格式：汉字（注音）
    '長い間（ながいあいだ）',
    '座り心地（すわりごこち）',
    '触り心地（さわりごこち）',
    '申し訳（もうしわけ）',
    '出張（しゅっちょう）',
    '大好き（だいすき）',
    '唐揚げ（からあげ）',
    '立ち読み（たちよみ）',
    '１杯（いっぱい）',
    '１回（いっかい）',
    '１泊（いっぱく）',
    '１か月（いっかげつ）',
    '１か月間（いっかげつかん）',
    '試験（しけん）',
    '使用（しよう）',
    '前（まえ）',
    '待（ま）',
    '日記（にっき）',
    '話し手（はなして）',
    '聞き手（ききて）',
    '以上（いじょう）',
    '使い方（つかいかた）',
    '０点（れいてん）',
    '買い物（かいもの）',
    '動作（どうさ）',
    'm（メートル）',
    '味覚 （みかく）',
    '気持ち（きもち）',
    '青い色（あおいいろ）',
    '吐き気（はきけ）',
    '元カレ（もとかれ）',
    '髪の毛（かみのけ）',
    '駅（えき）',
    '万引き（まんびき）',
    '通（どお）',
    '遅刻（ちこく）',
    '経（た）',
    '三分の一（さんぶんのいち）',
    '折があれば（おりがあれば）',
    '折を見て（おりをみて）',
    '折に触れて（おりにふれて）',
    '折も折（おりもおり）',
    '残業（ざんぎょう）',
    '合（あ）',
    '楽（たの）',
    '貸し借り（かしかり）',
    '入学（にゅうがく）',
    '暮（ぐ）',
    '届け出（とどけで）',
    '有名（ゆうめい）',
    '自身（じしん）',
    '住（す）',
    '夕ご飯（ゆうごはん）',
    '星の数（ほしのかず）',
    '窓の外（まどのそと）',
    '考え方（かんがえかた）',
    '感じ方（かんじかた）',
    //'付き合（つきあい）',
    '貯（た）',
    '悩み事（なやみごと）',
    '寄り道（よりみち）',
    '歩（ある）',
    //'泣きっ面に蜂（なきっつらにはち）',
    '食べず嫌い（たべずぎらい）',
    'アタック（attack）',
    'お茶する（おちゃする）',
    '入（はい）',
    '使い分け（つかいわけ）',
    '行き渡る（いきわたる）',
    //'目の色が変わる（めのいろがかわる）',
    //'目の色を変える（めのいろをかえる）',

    // 强制注音
    { pattern: '羽根を伸ばす（羽根を伸ばす）', reading: 'はねをのばす' },
    { pattern: '長蛇の列（長蛇の列）', reading: 'ちょうだのれつ' },
    { pattern: '付き合（つきあい）', reading: 'つきあ' },

    // 强制替换
    { pattern: '目に余る②（めにあまる）', replacement: '<ruby>目<rt>め</rt></ruby>に<ruby>余<rt>あま</rt></ruby>る②' },
    { pattern: '言い方（いいかた）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>' },
    { pattern: '言い訳（いいわけ）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>訳<rt>わけ</rt></ruby>' },
    {
      pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）',
      replacement:
        '<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える',
    },
    {
      pattern: '水の泡になる・水の泡となる（みずのあわになる）',
      replacement: '<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>になる・<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>となる',
    },
    {
      pattern: '意味で（いみ）',
      replacement: '<ruby>意味<rt>いみ</rt></ruby>で',
    },
    {
      pattern: '和製英語で（わせいえいご）',
      replacement: '<ruby>和製英語<rt>わせいえいご</rt></ruby>で',
    },
  ]

  // HTML级别的替换规则
  const HTML_REPLACEMENT_RULES = [
    { pattern: /一瞬（いっしゅん<br>）/g, replacement: '<ruby>一瞬<rt>いっしゅん</rt></ruby>' },
    {
      pattern: /<b><span style="font-size: 125%;">居<\/span><\/b>（い）/g,
      replacement: '<b><ruby>居<rt>い</rt></ruby></b>',
    },
    {
      pattern: /<b style="font-size: large;">留守<\/b>（るす）/g,
      replacement: '<b><ruby>留守<rt>るす</rt></ruby></b>',
    },
  ]

  // 始终不转换的注音模式
  const ALWAYS_EXCLUDE = new Set(['挙句（に）', '道草（を）', '以上（は）', '人称（私）', '人称（あなた）', '矢先（に）'])
  // 助词排除列表，且前面非汉字。适用于 'あげく（に）' 不转换
  const RUBY_EXCLUDE_PARTICLES = new Set(['に', 'は', 'を', 'が', 'の', 'と', 'で', 'から', 'まで', 'へ', 'も', 'や', 'ね', 'よ', 'さ'])

  // 正则表达式匹配注音格式：汉字（平假名）
  const RUBY_REGEX = /([一-龯々]+)（([^（）]*)）/g
  // 正则表达式匹配片假名注音格式：片假名（拉丁字母）
  const KATAKANA_RUBY_REGEX = /([\u30A0-\u30FF]+)[（(]([\w\s+]+)[）)]/g
  // 全平假名
  const KANA_ONLY_REGEX = /^[\u3040-\u309F]+$/
  // 非平假名字符
  const NON_KANA_REGEX = /[^\u3040-\u309F]/
  // 单个平假名字符
  const IS_KANA_CHAR_REGEX = /^[\u3040-\u309F]$/
  // 匹配【...(...)】格式
  const BRACKET_RUBY_REGEX = /[【「]([^【】「」（）]*)（([^（）]*)）([^【】「」（）]*)[】」]/g
  // 图片链接正则，匹配 livedoor.blogimg.jp 的图片链接
  const IMG_SRC_REGEX = /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i
  // 全局移除的选择器
  const GLOBAL_REMOVE_SELECTORS = [
    'header#blog-header',
    'footer#blog-footer',
    '.ldb_menu',
    '.article-social-btn',
    '.adsbygoogle',
    'a[href*="blogmura.com"]',
    'a[href*="with2.net"]',
  ]

  // --- 开关控制功能 (始终执行) ---
  const TOGGLE_KEY = 'ruby_converter_enabled'

  // ========================================
  // 工具函数组 - 基础辅助函数
  // ========================================

  /**
   * 正则表达式特殊字符转义函数
   * @description 将字符串中的正则表达式特殊字符进行转义，使其可以安全地用于正则表达式中
   * @param {string} string - 需要转义的字符串
   * @returns {string} 转义后的字符串
   * @example escapeRegExp("hello(world)") => "hello\\(world\\)"
   */
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // ========================================
  // 文本处理函数组 - 核心业务逻辑
  // ========================================

  /**
   * 解析复合词条目
   * @description 解析不同格式的复合词条目，支持字符串格式和对象格式
   * @param {string|object} entry - 复合词条目，可以是字符串或对象
   * @returns {object|null} 解析后的对象，包含 pattern、kanji、reading 或 replacement 属性
   * @example
   *   parseCompoundEntry('長い間（ながいあいだ）') => {pattern: '長い間（ながいあいだ）', kanji: '長い間', reading: 'ながいあいだ'}
   *   parseCompoundEntry({pattern: '...', replacement: '...'}) => {pattern: '...', replacement: '...'}
   */
  function parseCompoundEntry(entry) {
    // 字符串格式："汉字（注音）"
    if (typeof entry === 'string') {
      const leftIdx = entry.indexOf('（')
      const rightIdx = entry.lastIndexOf('）')
      if (leftIdx > 0 && rightIdx > leftIdx) {
        return {
          pattern: entry,
          kanji: entry.slice(0, leftIdx),
          reading: entry.slice(leftIdx + 1, rightIdx),
        }
      }
    }
    // 对象格式：{ pattern: '...', reading: '...' }
    else if (entry && entry.reading) {
      return {
        pattern: entry.pattern,
        kanji: entry.pattern.replace(/（.*?）/, ''),
        reading: entry.reading,
      }
    }
    // 对象格式：{ pattern: '...', replacement: '...' }
    else if (entry && entry.replacement) {
      return entry
    }
    return null
  }

  /**
   * 分割复合词并生成 Ruby 标签
   * @description 将复合词的汉字和读音进行智能分割，生成对应的 Ruby 标签
   * @param {string} kanji - 汉字部分
   * @param {string} reading - 读音部分
   * @returns {string} 生成的 HTML Ruby 标签字符串
   * @example segmentCompoundWord('長い間', 'ながいあいだ') => '<ruby>長<rt>なが</rt></ruby>い<ruby>間<rt>あいだ</rt></ruby>'
   */
  function segmentCompoundWord(kanji, reading) {
    const segments = []
    let kanjiIndex = 0
    let readingIndex = 0

    while (kanjiIndex < kanji.length) {
      // 检查当前字符是否为平假名
      if (IS_KANA_CHAR_REGEX.test(kanji[kanjiIndex])) {
        // 如果是平假名，直接添加到结果中
        segments.push(kanji[kanjiIndex])
        kanjiIndex++
        // 在注音中找到对应的平假名位置
        readingIndex = reading.indexOf(kanji[kanjiIndex - 1], readingIndex) + 1
      } else {
        // 处理连续的汉字部分
        let kanjiPart = ''
        let readingPart = ''

        // 收集连续的汉字
        while (kanjiIndex < kanji.length && !IS_KANA_CHAR_REGEX.test(kanji[kanjiIndex])) {
          kanjiPart += kanji[kanjiIndex]
          kanjiIndex++
        }

        // 确定对应的注音部分
        const nextKanaIndex = kanjiIndex < kanji.length ? reading.indexOf(kanji[kanjiIndex], readingIndex) : reading.length

        readingPart = reading.substring(readingIndex, nextKanaIndex)
        readingIndex = nextKanaIndex

        // 创建ruby标签
        segments.push(`<ruby>${kanjiPart}<rt>${readingPart}</rt></ruby>`)
      }
    }

    return segments.join('')
  }

  /**
   * 处理文本内容中的注音转换
   * @description 将文本中的注音格式转换为 Ruby 标签，支持复合词处理和多种注音格式
   * @param {string} text - 需要处理的文本内容
   * @returns {string} 处理后的文本内容，包含 Ruby 标签
   * @example processTextContent('長い間（ながいあいだ）') => '<ruby>長<rt>なが</rt></ruby>い<ruby>間<rt>あいだ</rt></ruby>'
   */
  function processTextContent(text) {
    // 优化：提前检查文本是否包含可能的注音格式，避免不必要的处理
    if (!text.includes('（') && !text.includes('(')) {
      return text
    }

    // 处理复合词（支持多种格式）
    COMPOUND_WORDS.forEach((compound) => {
      const parsed = parseCompoundEntry(compound)
      if (!parsed) return

      // 优化：先检查文本是否包含该模式，避免不必要的正则操作
      if (!text.includes(parsed.pattern.split('（')[0])) return

      // 直接替换型
      if (parsed.replacement) {
        const pattern = escapeRegExp(parsed.pattern)
        text = text.replace(new RegExp(pattern, 'g'), parsed.replacement)
      }
      // 注音解析型
      else if (parsed.kanji && parsed.reading) {
        const pattern = escapeRegExp(parsed.pattern)
        const replacement = segmentCompoundWord(parsed.kanji, parsed.reading)
        text = text.replace(new RegExp(pattern, 'g'), replacement)
      }
    })

    // 处理常规注音

    // 1. 片假名+英文注音处理（支持全角/半角括号）
    text = text.replace(KATAKANA_RUBY_REGEX, (_, katakana, romaji) => {
      // 直接整体作为注音，不拆分 +
      return `<ruby>${katakana}<rt>${romaji}</rt></ruby>`
    })

    // 2. 汉字（平假名）注音处理
    return text.replace(RUBY_REGEX, (_, kanji, reading) => {
      const fullMatch = kanji + '（' + reading + '）'

      // 检查是否在始终不转换列表中
      if (ALWAYS_EXCLUDE.has(fullMatch)) {
        return _
      }

      // 排除助词且 kanji 全为平假名
      if (RUBY_EXCLUDE_PARTICLES.has(reading) && KANA_ONLY_REGEX.test(kanji)) return _
      // 排除非假名注音
      if (NON_KANA_REGEX.test(reading)) return _

      return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : _
    })
  }

  // ========================================
  // DOM操作函数组 - DOM相关操作
  // ========================================

  /**
   * 清理容器开头和结尾的空白和换行
   * @description 移除容器开头和结尾的空白文本节点、换行符和空格元素
   * @param {HTMLElement} container - 需要清理的容器元素
   * @returns {void}
   * @example trimContainerBreaks(document.querySelector('.article-body'))
   */
  function trimContainerBreaks(container) {
    // 清理开头的空白
    let node = container.firstChild
    while (node) {
      const nextNode = node.nextSibling
      if (
        (node.nodeType === Node.TEXT_NODE && /^\s*$/.test(node.textContent)) ||
        (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'BR') ||
        (node.nodeType === Node.ELEMENT_NODE && node.innerHTML === '&nbsp;' && node.tagName === 'SPAN')
      ) {
        container.removeChild(node)
      } else {
        break // 遇到非空白内容时停止
      }
      node = nextNode
    }

    // 清理结尾的空白
    node = container.lastChild
    while (node) {
      const prevNode = node.previousSibling
      if (
        (node.nodeType === Node.TEXT_NODE && /^\s*$/.test(node.textContent)) ||
        (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'BR') ||
        (node.nodeType === Node.ELEMENT_NODE && node.innerHTML === '&nbsp;' && node.tagName === 'SPAN')
      ) {
        container.removeChild(node)
      } else {
        break // 遇到非空白内容时停止
      }
      node = prevNode
    }
  }

  /**
   * 处理图片链接
   * @description 将博客图片链接转换为直接的图片元素，并优化图片显示
   * @param {HTMLElement} container - 包含图片链接的容器元素
   * @returns {void}
   * @example processImageLinks(document.querySelector('.article-body'))
   */
  function processImageLinks(container) {
    const imageLinks = container.querySelectorAll('a[href*="livedoor.blogimg.jp"]')
    imageLinks.forEach((link) => {
      const img = link.querySelector('img.pict')
      if (!img) return
      // 获取原始图片 URL（移除 -s 后缀）
      const originalSrc = img.src.replace(IMG_SRC_REGEX, '$1$2')
      // 创建新的图片元素
      const newImg = document.createElement('img')
      newImg.src = originalSrc
      newImg.alt = (img.alt || '').replace(/blog/gi, '')
      newImg.className = img.className
      newImg.width = img.width
      newImg.height = img.height
      // 替换链接为图片
      link.replaceWith(newImg)
    })
  }

  /**
   * 移除全局不需要的元素
   * @description 批量移除页面中不需要的全局元素，如广告、菜单等
   * @returns {void}
   * @example removeGlobalElements()
   */
  function removeGlobalElements() {
    // 优化：合并选择器，一次查询完成所有元素移除，减少 DOM 遍历次数
    const combinedSelector = GLOBAL_REMOVE_SELECTORS.join(',')
    const elementsToRemove = document.querySelectorAll(combinedSelector)

    // 批量移除元素，减少重排重绘
    elementsToRemove.forEach((el) => el.remove())
  }

  /**
   * 清理文章内容
   * @description 清理文章容器中的不需要元素，包括广告、脚本、不需要的链接等
   * @param {HTMLElement} container - 文章容器元素
   * @returns {void}
   * @example cleanupContent(document.querySelector('.article-body-inner'))
   */
  function cleanupContent(container) {
    // 0. 处理图片链接
    processImageLinks(container)

    // 1. 批量收集需要移除的元素，减少 DOM 查询次数
    const elementsToRemove = []

    // 收集不需要的链接
    const unwantedLinks = container.querySelectorAll('a[href*="blogmura.com"], a[href*="with2.net"]')
    elementsToRemove.push(...unwantedLinks)

    // 收集脚本元素
    const scripts = container.querySelectorAll('script')
    elementsToRemove.push(...scripts)

    // 2. 查找 ad2 元素并收集其后续元素
    const adDiv = container.querySelector('#ad2')
    if (adDiv) {
      // 收集 ad2 之后的所有元素
      let nextElement = adDiv.nextElementSibling
      while (nextElement) {
        elementsToRemove.push(nextElement)
        nextElement = nextElement.nextElementSibling
      }
      // 收集 ad2 本身
      elementsToRemove.push(adDiv)
    }

    // 3. 批量移除所有收集的元素，减少重排重绘次数
    elementsToRemove.forEach((el) => el.remove())

    // 4. 清理容器末尾的空白和换行
    trimContainerBreaks(container)
  }

  // 优化：使用 Set 进行快速去重，避免数组遍历
  const COMPOUND_WORDS_SET = new Set(COMPOUND_WORDS)

  /**
   * 扫描和学习新词条
   * @description 从页面内容中扫描新的复合词条并注册到词典中
   * @param {HTMLElement} element - 需要扫描的元素
   * @returns {void}
   * @example findAndRegisterCompounds(document.querySelector('.article-body'))
   */
  function findAndRegisterCompounds(element) {
    if (!element) return
    const htmlContent = element.innerHTML
    let match
    while ((match = BRACKET_RUBY_REGEX.exec(htmlContent)) !== null) {
      const reading = match[2]
      if (reading && !NON_KANA_REGEX.test(reading)) {
        const compound = match[1] + '（' + match[2] + '）' + match[3]
        // 优化：使用 Set 的 has() 方法，O(1) 时间复杂度，避免数组的 O(n) 查找
        if (!COMPOUND_WORDS_SET.has(compound)) {
          COMPOUND_WORDS.push(compound)
          COMPOUND_WORDS_SET.add(compound)
        }
      }
    }
  }

  // --- 文本内容处理 ---
  function processTextContent(text) {
    // 优化：提前检查文本是否包含可能的注音格式，避免不必要的处理
    if (!text.includes('（') && !text.includes('(')) {
      return text
    }

    // 处理复合词（支持多种格式）
    COMPOUND_WORDS.forEach((compound) => {
      const parsed = parseCompoundEntry(compound)
      if (!parsed) return

      // 优化：先检查文本是否包含该模式，避免不必要的正则操作
      if (!text.includes(parsed.pattern.split('（')[0])) return

      // 直接替换型
      if (parsed.replacement) {
        const pattern = escapeRegExp(parsed.pattern)
        text = text.replace(new RegExp(pattern, 'g'), parsed.replacement)
      }
      // 注音解析型
      else if (parsed.kanji && parsed.reading) {
        const pattern = escapeRegExp(parsed.pattern)
        const replacement = segmentCompoundWord(parsed.kanji, parsed.reading)
        text = text.replace(new RegExp(pattern, 'g'), replacement)
      }
    })

    // 处理常规注音

    // 1. 片假名+英文注音处理（支持全角/半角括号）
    text = text.replace(KATAKANA_RUBY_REGEX, (_, katakana, romaji) => {
      // 直接整体作为注音，不拆分 +
      return `<ruby>${katakana}<rt>${romaji}</rt></ruby>`
    })

    // 2. 汉字（平假名）注音处理
    return text.replace(RUBY_REGEX, (_, kanji, reading) => {
      const fullMatch = kanji + '（' + reading + '）'

      // 检查是否在始终不转换列表中
      if (ALWAYS_EXCLUDE.has(fullMatch)) {
        return _
      }

      // 排除助词且 kanji 全为平假名
      if (RUBY_EXCLUDE_PARTICLES.has(reading) && KANA_ONLY_REGEX.test(kanji)) return _
      // 排除非假名注音
      if (NON_KANA_REGEX.test(reading)) return _

      return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : _
    })
  }

  /**
   * Ruby 转换处理
   * @description 遍历指定根元素下的所有文本节点，将注音格式转换为 Ruby 标签
   * @param {HTMLElement} root - 根元素，将处理其下所有文本节点
   * @returns {void}
   * @example processRubyConversion(document.querySelector('.article-body'))
   */
  function processRubyConversion(root) {
    const treeWalker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
      acceptNode: (node) => (node.parentNode.nodeName !== 'SCRIPT' && node.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
    })

    // 优化：直接处理节点，避免收集所有节点到数组中，减少内存占用
    const nodesToProcess = []
    let node
    while ((node = treeWalker.nextNode())) {
      const newContent = processTextContent(node.nodeValue)
      if (newContent !== node.nodeValue) {
        // 只收集需要处理的节点信息，减少内存使用
        nodesToProcess.push({ node, newContent })
      }
    }

    // 批量处理需要替换的节点，避免频繁的 DOM 操作导致卡顿
    nodesToProcess.forEach(({ node, newContent }) => {
      const wrapper = document.createElement('span')
      wrapper.innerHTML = newContent
      node.replaceWith(...wrapper.childNodes)
    })
  }

  /**
   * 应用 HTML 替换规则
   * @description 对指定元素应用预定义的 HTML 替换规则
   * @param {HTMLElement} element - 需要应用替换的元素
   * @param {Array} rules - 替换规则数组，每个规则包含 pattern 和 replacement
   * @returns {void}
   * @example applyHtmlReplacements(element, HTML_REPLACEMENT_RULES)
   */
  function applyHtmlReplacements(element, rules) {
    if (!element || !rules || rules.length === 0) return
    let currentHTML = element.innerHTML
    const originalHTML = currentHTML
    rules.forEach((rule) => {
      currentHTML = currentHTML.replace(rule.pattern, rule.replacement)
    })
    if (currentHTML !== originalHTML) {
      element.innerHTML = currentHTML
    }
  }

  /**
   * 优化侧边栏显示
   * @description 清理侧边栏内容，只保留分类插件，并设置为可见
   * @returns {void}
   * @example optimizeSidebar()
   */
  function optimizeSidebar() {
    const sidebar = document.querySelector('aside#sidebar')
    if (!sidebar) return

    const category = sidebar.querySelector('.plugin-categorize')
    sidebar.textContent = ''
    if (category) {
      sidebar.appendChild(category)
      // 显示处理完成的侧边栏
      sidebar.style.visibility = 'visible'
    }
  }

  // ========================================
  // 样式注入函数组 - 样式相关
  // ========================================

  /**
   * 注入开关控制样式
   * @description 注入开关按钮和通知提示的 CSS 样式
   * @returns {void}
   * @example injectToggleStyles()
   */
  function injectToggleStyles() {
    const css = `
      /* 开关容器 */
      #ruby-converter-toggle {
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: white;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      /* 开关标签 */
      #ruby-converter-toggle label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #4B5563;
        cursor: pointer;
      }

      /* 开关主体 */
      .toggle-switch {
        position: relative;
        display: inline-block;
        width: 3rem;
        height: 1.5rem;
      }

      /* 隐藏原始复选框 */
      .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      /* 开关滑块 */
      .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #E5E7EB;
        transition: .2s;
        border-radius: 9999px;
      }

      /* 开关圆形按钮 */
      .toggle-slider:before {
        position: absolute;
        content: "";
        height: 1.125rem;
        width: 1.125rem;
        left: 0.1875rem;
        bottom: 0.1875rem;
        background-color: white;
        transition: .2s;
        border-radius: 50%;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      }

      /* 开启状态 */
      input:checked + .toggle-slider {
        background-color: #3B82F6;
      }

      input:checked + .toggle-slider:before {
        transform: translateX(1.5rem);
      }

      /* 提示信息 */
      .ruby-converter-notification {
        position: fixed;
        top: 4rem;
        right: 1rem;
        z-index: 10000;
        padding: 0.75rem 1rem;
        background-color: #3B82F6;
        color: white;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        font-size: 0.875rem;
        font-weight: 500;
        animation: fadeInOut 2s ease-in-out;
      }

      @keyframes fadeInOut {
        0% { opacity: 0; transform: translateY(-10px); }
        20% { opacity: 1; transform: translateY(0); }
        80% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(-10px); }
      }
    `
    const style = document.createElement('style')
    style.textContent = css
    ;(document.head || document.documentElement).appendChild(style)
  }

  /**
   * 注入初始页面样式
   * @description 注入页面布局、样式优化和元素隐藏的 CSS 样式，防止页面闪烁
   * @returns {void}
   * @example injectInitialStyles()
   */
  function injectInitialStyles() {
    const css = `
      /* 基本布局 */
      #container {
        width: 100%;
      }
      @media (min-width: 960px) {
        #container {
          max-width: 960px;
        }
      }
      @media (min-width: 1040px) {
        #container {
          max-width: 1040px;
        }
      }
      #content {
        display: flex;
        position: relative;
        padding: 50px 0 !important;
      }
      #main {
        flex: 1;
        float: none !important;
        width: 100% !important;
      }

      /* 侧边栏 */
      aside#sidebar {
        visibility: hidden;
        float: none !important;
        width: 350px !important;
        flex: 0 0 350px;
      }
      .plugin-categorize {
        position: fixed;
        height: 85vh;
        display: flex;
        flex-direction: column;
        padding: 0 !important;
        width: 350px !important;
      }
      .plugin-categorize .side {
        flex: 1;
        overflow-y: auto;
        max-height: unset;
      }
      .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) {
        margin-top: 5px;
        margin-bottom: 0;
      }

      /* 文章内容 */
      .article {
        padding: 0 0 20px 0 !important;
        margin-bottom: 30px !important;
      }
      .article-body {
        padding: 0 !important;
      }
      .article-pager {
        margin-bottom: 0 !important;
      }
      .article-body-inner {
        line-height: 2;
      }
      .article-body-inner img.pict {
        margin: 0 !important;
        width: 80% !important;
        display: block;
      }
      .article-body-inner strike {
        color: orange;
      }
      .article-body-inner iframe {
        margin: 4px 0 !important;
      }

      /* 返回顶部按钮 */
      .to-pagetop {
        position: fixed;
        bottom: 20px;
        right: 100px;
        z-index: 1000;
      }

      /* 禁用文本选择和鼠标事件 */
      rt {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        pointer-events: none;
      }

      /* 隐藏不必要的元素 */
      header#blog-header,
      footer#blog-footer,
      .ldb_menu,
      .article-social-btn,
      .adsbygoogle,
      #ldblog_related_articles_01d4ecf1,
      #ad2 {
        display: none !important;
      }

      /* 覆盖清除浮动样式 */
      .article-body-inner:after,
      .article-meta:after,
      #container:after,
      #content:after,
      article:after,
      section:after,
      .cf:after {
        content: none !important;
        display: none !important;
        height: auto !important;
        visibility: visible !important;
      }
    `
    if (typeof GM_addStyle === 'function') {
      GM_addStyle(css)
    } else {
      const style = document.createElement('style')
      style.textContent = css
      ;(document.head || document.documentElement).appendChild(style)
    }
  }

  // ========================================
  // 控制函数组 - 用户交互控制
  // ========================================

  /**
   * 创建开关控制元素
   * @description 创建页面右上角的开关按钮，用于控制脚本功能的开启和关闭
   * @returns {void}
   * @example createToggle()
   */
  function createToggle() {
    const enabled = GM_getValue(TOGGLE_KEY, true)
    const container = document.createElement('div')
    container.id = 'ruby-converter-toggle'
    container.innerHTML = `
      <label for="ruby-toggle-switch">簡潔モード</label>
      <label class="toggle-switch">
        <input type="checkbox" id="ruby-toggle-switch" ${enabled ? 'checked' : ''}>
        <span class="toggle-slider"></span>
      </label>
    `
    document.body.appendChild(container)
    // 添加切换事件
    container.querySelector('input').addEventListener('change', (e) => {
      GM_setValue(TOGGLE_KEY, e.target.checked)
      // 显示提示
      const msg = document.createElement('div')
      msg.className = 'ruby-converter-notification'
      msg.textContent = '設定を保存しました。ページを再読み込みしてください。'
      document.body.appendChild(msg)
      setTimeout(() => msg.remove(), 2000)
    })
  }

  // ========================================
  // 主执行函数组 - 程序入口和流程控制
  // ========================================

  // --- 主功能 (仅在开关开启时执行) ---
  if (!GM_getValue(TOGGLE_KEY, true)) {
    // 注入开关样式
    injectToggleStyles()
    if (document.readyState === 'complete') {
      createToggle()
    } else {
      document.addEventListener('DOMContentLoaded', createToggle)
    }
    return
  }

  // --- 第一步：尽早注入 CSS 防止闪烁 ---
  injectToggleStyles()
  injectInitialStyles()

  /**
   * 主执行函数
   * @description 脚本的主要执行流程，协调各个功能模块的执行
   * @returns {void}
   * @example main()
   */
  function main() {
    // 阶段 1: 立即移除全局元素
    removeGlobalElements()

    // 阶段 2: 当 DOM 内容加载完成后处理主要内容
    const processMainContent = () => {
      const articleBodies = document.querySelectorAll('.article-body-inner')

      // 优化：分批处理文章内容，避免长时间阻塞主线程导致卡顿
      if (articleBodies.length === 0) return

      let currentIndex = 0
      const processBatch = () => {
        const batchSize = Math.min(2, articleBodies.length - currentIndex) // 每批处理最多2个元素
        const endIndex = currentIndex + batchSize

        for (let i = currentIndex; i < endIndex; i++) {
          const body = articleBodies[i]
          cleanupContent(body)
          applyHtmlReplacements(body, HTML_REPLACEMENT_RULES)
          findAndRegisterCompounds(body)
          processRubyConversion(body)
          // 显示处理完成的内容
          body.style.opacity = 1
        }

        currentIndex = endIndex

        // 如果还有未处理的内容，使用 requestAnimationFrame 继续处理
        if (currentIndex < articleBodies.length) {
          requestAnimationFrame(processBatch)
        } else {
          // 所有内容处理完成后优化侧边栏
          optimizeSidebar()
        }
      }

      // 开始分批处理
      processBatch()
    }

    // 优化：移除 MutationObserver，使用简单的重试机制
    // 由于页面内容是静态加载的，不需要复杂的 DOM 监听
    const tryProcessContent = (retryCount = 0) => {
      const articleBodies = document.querySelectorAll('.article-body-inner')

      if (articleBodies.length > 0) {
        processMainContent()
      } else if (retryCount < 3) {
        // 最多重试 3 次，每次间隔 100ms
        setTimeout(() => tryProcessContent(retryCount + 1), 100)
      } else {
        console.warn('Tampermonkey script: Could not find .article-body-inner after retries')
      }
    }

    tryProcessContent()
  }

  /**
   * 初始化脚本
   * @description 脚本的初始化函数，启动主要功能和创建控制界面
   * @returns {void}
   * @example initializeScript()
   */
  function initializeScript() {
    main()
    createToggle()
  }

  // --- 启动脚本 ---
  // 根据 DOM 加载状态选择合适的启动时机
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeScript, { once: true })
  } else {
    // DOM 已经加载完成，直接执行
    initializeScript()
  }
})()
